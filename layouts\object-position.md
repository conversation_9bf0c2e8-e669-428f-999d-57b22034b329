- **Property:** object-position
- **Shorthand:** op  
  Utilities for controlling the position of replaced elements (like images) within their container.

```css
opC {
  object-position: center;
}
opT {
  object-position: top;
}
opB {
  object-position: bottom;
}
opL {
  object-position: left;
}
opR {
  object-position: right;
}
opLt {
  object-position: left top;
}
opRt {
  object-position: right top;
}
opLb {
  object-position: left bottom;
}
opRb {
  object-position: right bottom;
}
```

The `object-position` property specifies the alignment of the replaced element's content within its container. This is particularly useful when combined with `object-fit: cover` or `object-fit: contain` to control which part of the image is visible when the image is cropped or scaled.

## Complete Object Position Grid Example

The following 3x3 grid demonstrates all nine object-position utility classes using the same image. Each position shows how the image is cropped and positioned within identical containers:

```html
<div class="dG gtc1fr;1fr;1fr gap15px p20px bgcLightGray brd1px;solid;#333">
  <!-- Row 1: Top positions -->
  <div class="p10px bgcWhite brd1px;solid;#333">
    <h4 class="mB10px tac fz14px">opLt</h4>
    <div class="w200px h150px bgcLightGray brd1px;solid;#333">
      <img
        src="layouts/img/nha-trang-beaches-1.webp"
        alt="Beach scene"
        class="ofCv opLt w100% h100%"
      />
    </div>
    <p class="mT5px fz12px tac">Left Top</p>
  </div>

  <div class="p10px bgcWhite brd1px;solid;#333">
    <h4 class="mB10px tac fz14px">opT</h4>
    <div class="w200px h150px bgcLightGray brd1px;solid;#333">
      <img
        src="layouts/img/nha-trang-beaches-1.webp"
        alt="Beach scene"
        class="ofCv opT w100% h100%"
      />
    </div>
    <p class="mT5px fz12px tac">Top</p>
  </div>

  <div class="p10px bgcWhite brd1px;solid;#333">
    <h4 class="mB10px tac fz14px">opRt</h4>
    <div class="w200px h150px bgcLightGray brd1px;solid;#333">
      <img
        src="layouts/img/nha-trang-beaches-1.webp"
        alt="Beach scene"
        class="ofCv opRt w100% h100%"
      />
    </div>
    <p class="mT5px fz12px tac">Right Top</p>
  </div>

  <!-- Row 2: Middle positions -->
  <div class="p10px bgcWhite brd1px;solid;#333">
    <h4 class="mB10px tac fz14px">opL</h4>
    <div class="w200px h150px bgcLightGray brd1px;solid;#333">
      <img
        src="layouts/img/nha-trang-beaches-1.webp"
        alt="Beach scene"
        class="ofCv opL w100% h100%"
      />
    </div>
    <p class="mT5px fz12px tac">Left</p>
  </div>

  <div class="p10px bgcWhite brd1px;solid;#333">
    <h4 class="mB10px tac fz14px">opC</h4>
    <div class="w200px h150px bgcLightGray brd1px;solid;#333">
      <img
        src="layouts/img/nha-trang-beaches-1.webp"
        alt="Beach scene"
        class="ofCv opC w100% h100%"
      />
    </div>
    <p class="mT5px fz12px tac">Center</p>
  </div>

  <div class="p10px bgcWhite brd1px;solid;#333">
    <h4 class="mB10px tac fz14px">opR</h4>
    <div class="w200px h150px bgcLightGray brd1px;solid;#333">
      <img
        src="layouts/img/nha-trang-beaches-1.webp"
        alt="Beach scene"
        class="ofCv opR w100% h100%"
      />
    </div>
    <p class="mT5px fz12px tac">Right</p>
  </div>

  <!-- Row 3: Bottom positions -->
  <div class="p10px bgcWhite brd1px;solid;#333">
    <h4 class="mB10px tac fz14px">opLb</h4>
    <div class="w200px h150px bgcLightGray brd1px;solid;#333">
      <img
        src="layouts/img/nha-trang-beaches-1.webp"
        alt="Beach scene"
        class="ofCv opLb w100% h100%"
      />
    </div>
    <p class="mT5px fz12px tac">Left Bottom</p>
  </div>

  <div class="p10px bgcWhite brd1px;solid;#333">
    <h4 class="mB10px tac fz14px">opB</h4>
    <div class="w200px h150px bgcLightGray brd1px;solid;#333">
      <img
        src="layouts/img/nha-trang-beaches-1.webp"
        alt="Beach scene"
        class="ofCv opB w100% h100%"
      />
    </div>
    <p class="mT5px fz12px tac">Bottom</p>
  </div>

  <div class="p10px bgcWhite brd1px;solid;#333">
    <h4 class="mB10px tac fz14px">opRb</h4>
    <div class="w200px h150px bgcLightGray brd1px;solid;#333">
      <img
        src="layouts/img/nha-trang-beaches-1.webp"
        alt="Beach scene"
        class="ofCv opRb w100% h100%"
      />
    </div>
    <p class="mT5px fz12px tac">Right Bottom</p>
  </div>
</div>
```

This comprehensive grid shows how each object-position utility affects the same image when combined with `object-fit: cover`. Notice how different parts of the beach scene become visible depending on the positioning value used.

![Object position grid example](./img/object-position/grid.png)
