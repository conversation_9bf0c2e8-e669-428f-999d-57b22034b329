- **Property:** object-position
- **Shorthand:** op  
  Utilities for controlling the position of replaced elements (like images) within their container.

```css
opC {
  object-position: center;
}
opT {
  object-position: top;
}
opB {
  object-position: bottom;
}
opL {
  object-position: left;
}
opR {
  object-position: right;
}
opLt {
  object-position: left top;
}
opRt {
  object-position: right top;
}
opLb {
  object-position: left bottom;
}
opRb {
  object-position: right bottom;
}
```

The `object-position` property specifies the alignment of the replaced element's content within its container. This is particularly useful when combined with `object-fit: cover` or `object-fit: contain` to control which part of the image is visible when the image is cropped or scaled.

## Object Position Center

```css
opC {
  object-position: center;
}
```

Centers the image both horizontally and vertically within its container. This is the default behavior and ensures the most important part of the image (typically the center) remains visible.

**Example:**

```html
<div class="w300px h200px bgcLightGray brd1px;solid;#333">
  <img
    src="layouts/img/nha-trang-beaches-1.webp"
    alt="Beach scene"
    class="ofCv opC w100% h100%"
  />
</div>
```

![Object position center example](./img/object-position/center.png)

## Object Position Top

```css
opT {
  object-position: top;
}
```

Aligns the image to the top of its container while keeping it horizontally centered. Useful for images where the important content is at the top.

**Example:**

```html
<div class="w300px h200px bgcLightGray brd1px;solid;#333">
  <img
    src="layouts/img/nha-trang-beaches-1.webp"
    alt="Beach scene"
    class="ofCv opT w100% h100%"
  />
</div>
```

![Object position top example](./img/object-position/top.png)

## Object Position Bottom

```css
opB {
  object-position: bottom;
}
```

Aligns the image to the bottom of its container while keeping it horizontally centered. Perfect for images where the important content is at the bottom.

**Example:**

```html
<div class="w300px h200px bgcLightGray brd1px;solid;#333">
  <img
    src="layouts/img/nha-trang-beaches-1.webp"
    alt="Beach scene"
    class="ofCv opB w100% h100%"
  />
</div>
```

![Object position bottom example](./img/object-position/bottom.png)

## Object Position Left

```css
opL {
  object-position: left;
}
```

Aligns the image to the left side of its container while keeping it vertically centered. Ideal for images where the focal point is on the left side.

**Example:**

```html
<div class="w300px h200px bgcLightGray brd1px;solid;#333">
  <img
    src="layouts/img/nha-trang-beaches-1.webp"
    alt="Beach scene"
    class="ofCv opL w100% h100%"
  />
</div>
```

![Object position left example](./img/object-position/left.png)

## Object Position Right

```css
opR {
  object-position: right;
}
```

Aligns the image to the right side of its container while keeping it vertically centered. Perfect for images where the important content is on the right side.

**Example:**

```html
<div class="w300px h200px bgcLightGray brd1px;solid;#333">
  <img
    src="layouts/img/nha-trang-beaches-1.webp"
    alt="Beach scene"
    class="ofCv opR w100% h100%"
  />
</div>
```

![Object position right example](./img/object-position/right.png)

## Object Position Left Top

```css
opLt {
  object-position: left top;
}
```

Positions the image at the top-left corner of its container. Useful for images where the important content is in the upper-left area.

**Example:**

```html
<div class="w300px h200px bgcLightGray brd1px;solid;#333">
  <img
    src="layouts/img/nha-trang-beaches-1.webp"
    alt="Beach scene"
    class="ofCv opLt w100% h100%"
  />
</div>
```

![Object position left top example](./img/object-position/left-top.png)

## Object Position Right Top

```css
opRt {
  object-position: right top;
}
```

Positions the image at the top-right corner of its container. Perfect for images where the focal point is in the upper-right area.

**Example:**

```html
<div class="w300px h200px bgcLightGray brd1px;solid;#333">
  <img
    src="layouts/img/nha-trang-beaches-1.webp"
    alt="Beach scene"
    class="ofCv opRt w100% h100%"
  />
</div>
```

![Object position right top example](./img/object-position/right-top.png)

## Object Position Left Bottom

```css
opLb {
  object-position: left bottom;
}
```

Positions the image at the bottom-left corner of its container. Ideal for images where the important content is in the lower-left area.

**Example:**

```html
<div class="w300px h200px bgcLightGray brd1px;solid;#333">
  <img
    src="layouts/img/nha-trang-beaches-1.webp"
    alt="Beach scene"
    class="ofCv opLb w100% h100%"
  />
</div>
```

![Object position left bottom example](./img/object-position/left-bottom.png)

## Object Position Right Bottom

```css
opRb {
  object-position: right bottom;
}
```

Positions the image at the bottom-right corner of its container. Perfect for images where the focal point is in the lower-right area.

**Example:**

```html
<div class="w300px h200px bgcLightGray brd1px;solid;#333">
  <img
    src="layouts/img/nha-trang-beaches-1.webp"
    alt="Beach scene"
    class="ofCv opRb w100% h100%"
  />
</div>
```

![Object position right bottom example](./img/object-position/right-bottom.png)

## Practical Applications

### Hero Section with Focal Point Control

```html
<div class="w100% h400px bgcLightGray">
  <img
    src="layouts/img/nha-trang-beaches-1.webp"
    alt="Beach scene"
    class="ofCv opT w100% h100%"
  />
</div>
```

Object-position utilities allow you to control which part of an image is visible in hero sections, ensuring the most important visual elements remain in view.

![Hero section example](./img/object-position/hero.png)

### Card Gallery with Consistent Cropping

```html
<div class="dG gtc1fr;1fr;1fr gap20px">
  <div class="w100% h200px bgcLightGray brd1px;solid;#333">
    <img
      src="layouts/img/nha-trang-beaches-1.webp"
      alt="Beach scene"
      class="ofCv opC w100% h100%"
    />
  </div>
  <div class="w100% h200px bgcLightGray brd1px;solid;#333">
    <img
      src="layouts/img/nha-trang-beaches-1.webp"
      alt="Beach scene"
      class="ofCv opT w100% h100%"
    />
  </div>
  <div class="w100% h200px bgcLightGray brd1px;solid;#333">
    <img
      src="layouts/img/nha-trang-beaches-1.webp"
      alt="Beach scene"
      class="ofCv opB w100% h100%"
    />
  </div>
</div>
```

Object-position utilities ensure consistent image dimensions while allowing fine control over which part of each image is displayed.

![Card gallery example](./img/object-position/gallery.png)
